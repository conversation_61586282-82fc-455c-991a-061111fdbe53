/**
 * Matching Mayhem Game Controller
 * Server-side game logic for Matching Mayhem
 */

import type { Server, Socket } from 'socket.io';
import { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import { GAME_TYPES } from '../../utils/constants';
import { MATCHING_MAYHEM_CONFIG } from '../../utils/matchingMayhemConstants';
import type {
  GameInitResult,
  GameStartData,
  GameEndData,
  GameActionData,
  EndReason
} from '../../types/game';
import type {
  MatchingMayhemGameState,
  ServerRoundData,
  ServerCardData,
  ClientRoundData,
  CardSelectActionData,
  CardSelectResult,
  MatchingMayhemGameStartedData,
  MatchingMayhemActionResultData
} from '../../types/matchingMayhem';

export class MatchingMayhemController {
  private gameService: GameService;
  private gameStates: Map<string, MatchingMayhemGameState> = new Map();

  private roundTimers: Map<string, NodeJS.Timeout> = new Map(); // Only keep essential timeout timer
  
  private socketMap: Map<string, Socket> = new Map(); // Store socket references by roomId

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Generate a unique card ID
   */
  private generateCardId(): string {
    return 'card_' + Math.random().toString(36) + '_' + Date.now().toString(36);
  }

  /**
   * Convert server round data to client-safe round data (removes answer information)
   */
  private toClientRoundData(serverRoundData: ServerRoundData): ClientRoundData {
    return {
      roundNumber: serverRoundData.roundNumber,
      mainCard: serverRoundData.mainCard,
      cards: serverRoundData.cards.map(card => ({
        id: card.id,
        animalIndex: card.animalIndex,
        colorIndex: card.colorIndex,
        imageKey: card.imageKey,
        position: card.position
      })),
      startTime: serverRoundData.startTime,
      timeLimit: serverRoundData.timeLimit
    };
  }

  /**
   * Initialize a new Matching Mayhem game session (without starting timers)
   */
  initializeGame(roomId: string, socket: Socket): GameInitResult {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId);

    if (gameState) {
      // Game state exists, check if we can initialize
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      if (gameState.status === 'ended') {
        return { success: false, message: 'Game session has ended - no restarts allowed' };
      }
    } else {
      // Create new game state
      gameState = this.gameService.createGameState(
        roomId,
        GAME_TYPES.MATCHING_MAYHEM,
        MATCHING_MAYHEM_CONFIG.LIVES.INITIAL_LIVES
      );
    }

    // Initialize Matching Mayhem specific game state (hashmap, etc.)
    const matchingMayhemState: MatchingMayhemGameState = {
      currentRound: null,
      roundsCompleted: 0,
      isRoundActive: false,
      roundStartTime: null
    };

    this.gameStates.set(roomId, matchingMayhemState);

    // Store socket reference for later use
    this.socketMap.set(roomId, socket);

    // Generate the first round (but don't start it yet)
    const firstRound = this.generateNewRound(roomId, 1);
    if (!firstRound) {
      return { success: false, message: 'Failed to generate first round' };
    }

    logger.info(`Matching Mayhem game initialized for room ${roomId}`);

    return { success: true, gameState };
  }

  /**
   * Start a previously initialized Matching Mayhem game session
   */
  startGame(roomId: string, socket: Socket): GameInitResult {
    const gameState = this.gameService.getGameState(roomId);
    if (!gameState) {
      return { success: false, message: 'Game not initialized. Call initializeGame first.' };
    }

    if (gameState.status === 'active') {
      return { success: false, message: 'Game is already active' };
    }

    if (gameState.status === 'ended') {
      return { success: false, message: 'Game session has ended - no restarts allowed' };
    }

    // Start the game timers and enable event acceptance
    const started = this.gameService.startGame(roomId, socket);
    if (!started) {
      return { success: false, message: 'Failed to start game' };
    }

    // Start the first round
    const matchingMayhemState = this.gameStates.get(roomId);
    if (matchingMayhemState && matchingMayhemState.currentRound) {
      this.startRound(roomId, matchingMayhemState.currentRound);
    }

    logger.info(`Matching Mayhem game started for room ${roomId}`);

    return { success: true, gameState };
  }

  /**
   * Initialize and start a new Matching Mayhem game session (legacy method)
   * @deprecated Use initializeGame() followed by startGame() instead
   */
  initializeAndStartGame(roomId: string, socket: Socket): GameInitResult {
    const initResult = this.initializeGame(roomId, socket);
    if (!initResult.success) {
      return initResult;
    }

    return this.startGame(roomId, socket);
  }

  /**
   * Generate a new round with cards
   */
  private generateNewRound(roomId: string, roundNumber: number): ServerRoundData | null {
    try {
      // Choose a random animal and color for the main card
      const correctAnimalIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.ANIMAL_COUNT);
      const correctColorIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.COLOR_COUNT);

      // Create main card data
      const mainCard = {
        animalIndex: correctAnimalIndex,
        colorIndex: correctColorIndex,
        imageKey: `image_${correctColorIndex}_${correctAnimalIndex}`
      };

      // Choose a random position for the correct card (not center)
      let correctCardIndex: number;
      do {
        correctCardIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.CARD_COUNT);
      } while (correctCardIndex === MATCHING_MAYHEM_CONFIG.CENTER_CARD_INDEX);

      // Generate cards
      const cards: ServerCardData[] = [];
      const usedAnimalIndices = new Set<number>();
      usedAnimalIndices.add(correctAnimalIndex);

      // Get available animal indices for distractors
      const availableAnimalIndices = Array.from(
        { length: MATCHING_MAYHEM_CONFIG.ANIMAL_COUNT },
        (_, index) => index
      ).filter(index => index !== correctAnimalIndex);

      // Shuffle available animals
      for (let i = availableAnimalIndices.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [availableAnimalIndices[i], availableAnimalIndices[j]] = [availableAnimalIndices[j], availableAnimalIndices[i]];
      }

      let correctCardId = '';

      for (let i = 0; i < MATCHING_MAYHEM_CONFIG.CARD_COUNT; i++) {
        const cardId = this.generateCardId();

        if (i === correctCardIndex) {
          // This is the correct card - same animal, different color
          correctCardId = cardId;
          let cardColorIndex: number;
          do {
            cardColorIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.COLOR_COUNT);
          } while (cardColorIndex === correctColorIndex);

          cards.push({
            id: cardId,
            animalIndex: correctAnimalIndex,
            colorIndex: cardColorIndex,
            imageKey: `image_${cardColorIndex}_${correctAnimalIndex}`,
            isCorrect: true,
            position: i
          });
        } else if (i === MATCHING_MAYHEM_CONFIG.CENTER_CARD_INDEX) {
          // Center card - exact match to main card (distractor)
          cards.push({
            id: cardId,
            animalIndex: correctAnimalIndex,
            colorIndex: correctColorIndex,
            imageKey: `image_${correctColorIndex}_${correctAnimalIndex}`,
            isCorrect: false,
            position: i
          });
        } else {
          // Regular distractor card - different animal
          const distractorAnimalIndex = availableAnimalIndices.length > 0
            ? availableAnimalIndices.pop()!
            : Array.from(usedAnimalIndices).find(index => index !== correctAnimalIndex) || 0;

          const distractorColorIndex = Math.floor(Math.random() * MATCHING_MAYHEM_CONFIG.COLOR_COUNT);

          cards.push({
            id: cardId,
            animalIndex: distractorAnimalIndex,
            colorIndex: distractorColorIndex,
            imageKey: `image_${distractorColorIndex}_${distractorAnimalIndex}`,
            isCorrect: false,
            position: i
          });

          usedAnimalIndices.add(distractorAnimalIndex);
        }
      }

      const roundData: ServerRoundData = {
        roundNumber,
        correctCardIndex,
        correctCardId,
        mainCard,
        cards,
        startTime: Date.now(),
        timeLimit: MATCHING_MAYHEM_CONFIG.ROUND_TIME
      };

      logger.info(`Generated round ${roundNumber} for room ${roomId}, correct card at position ${correctCardIndex}`);
      return roundData;
    } catch (error) {
      logger.error(`Error generating round for room ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Start a round
   */
  private startRound(roomId: string, roundData: ServerRoundData): void {
    const matchingMayhemState = this.gameStates.get(roomId);
    const socket = this.socketMap.get(roomId);

    if (!matchingMayhemState || !socket) {
      logger.error(`No Matching Mayhem state or socket found for room ${roomId}`);
      return;
    }

    matchingMayhemState.currentRound = roundData;
    matchingMayhemState.isRoundActive = true;
    matchingMayhemState.roundStartTime = Date.now();

    // Clear any existing timers
    this.clearRoundTimers(roomId);

    // Set up round timer for timeout
    const timeoutTimer = setTimeout(() => {
      this.handleRoundTimeout(roomId);
    }, MATCHING_MAYHEM_CONFIG.ROUND_TIME);

    this.roundTimers.set(roomId, timeoutTimer);

    logger.info(`Started round ${roundData.roundNumber} in room ${roomId}`);
  }

  /**
   * Clear all timers for a room
   */
  private clearRoundTimers(roomId: string): void {
    // Clear timeout timer
    const timeoutTimer = this.roundTimers.get(roomId);
    if (timeoutTimer) {
      clearTimeout(timeoutTimer);
      this.roundTimers.delete(roomId);
    }
  }

  /**
   * Handle round timeout
   */
  private handleRoundTimeout(roomId: string): void {
    const matchingMayhemState = this.gameStates.get(roomId);
    const gameState = this.gameService.getGameState(roomId);

    // Check if round is still active and game is still running
    if (!matchingMayhemState || !matchingMayhemState.isRoundActive || !gameState || gameState.status !== 'active') {
      logger.info(`Round timeout ignored for room ${roomId}: round not active or game ended`);
      return;
    }

    logger.info(`Round timeout in room ${roomId}`);

    // Clear all timers for this round
    this.clearRoundTimers(roomId);

    this.setupNextRound(roomId);

    // Deduct life for timeout
    // const livesResult = this.gameService.deductLife(roomId);

    // if (livesResult.gameEnded) {
    //   this.endGame(roomId, 'no_lives');
    // } else {
    //   // Start next round
    //   this.setupNextRound(roomId);
    // }
  }

  /**
   * Setup next round
   */
  private setupNextRound(roomId: string): ServerRoundData | null {
    const matchingMayhemState = this.gameStates.get(roomId);
    const gameState = this.gameService.getGameState(roomId);

    // Check if game is still active and valid
    if (!matchingMayhemState || !gameState || gameState.status !== 'active') {
      logger.info(`Cannot setup next round for room ${roomId}: game not active or state missing`);
      return null;
    }

    matchingMayhemState.isRoundActive = false;
    matchingMayhemState.roundsCompleted++;

    // Generate and immediately start the next round
    const nextRound = this.generateNewRound(roomId, matchingMayhemState.roundsCompleted + 1);
    if (nextRound) {
      // Start the round immediately - no artificial delay needed
      this.startRound(roomId, nextRound);
      return nextRound;
    }

    logger.error(`Failed to generate next round for room ${roomId}`);
    return null;
  }

  /**
   * End the game
   */
  private endGame(roomId: string, reason: EndReason): void {
    const gameState = this.gameService.getGameState(roomId);
    const socket = this.socketMap.get(roomId);

    // Clear all timers
    this.clearRoundTimers(roomId);

    // End game in GameService
    this.gameService.endGame(roomId, reason);

    // Emit ended event to client if socket is available
    if (socket) {
      socket.emit('ended', {
        reason,
        finalScore: gameState?.score || 0
      });
    }

    // Clean up all game-specific data
    this.cleanupGame(roomId);

    logger.info(`Matching Mayhem game ended in room ${roomId}, reason: ${reason}, final score: ${gameState?.score || 0}`);
  }

  /**
   * Clean up game data when game ends or room is destroyed
   */
  private cleanupGame(roomId: string): void {
    // Clear all timers (redundant but safe)
    this.clearRoundTimers(roomId);

    // Clear game state and socket reference
    this.gameStates.delete(roomId);
    this.socketMap.delete(roomId);

    // Delete game state from GameService
    this.gameService.deleteGameState(roomId);

    logger.info(`Cleaned up Matching Mayhem game data for room ${roomId}`);
  }

  /**
   * Public cleanup method for external cleanup (e.g., on socket disconnect)
   */
  public cleanup(roomId: string): void {
    const gameState = this.gameService.getGameState(roomId);
    if (gameState && gameState.status === 'active') {
      // End the game due to disconnection
      this.endGame(roomId, 'manual');
    } else {
      // Just clean up data if game wasn't active
      this.cleanupGame(roomId);
    }
  }

  /**
   * Handle card selection
   */
  handleCardSelection(roomId: string, cardId: string, _reactionTime: number = 0): CardSelectResult & { nextRound?: ServerRoundData } {
    const gameState = this.gameService.getGameState(roomId);
    const matchingMayhemState = this.gameStates.get(roomId);

    // Check if game and state are valid
    if (!gameState || !matchingMayhemState || gameState.status !== 'active') {
      logger.info(`Card selection ignored for room ${roomId}: game not active or state missing`);
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState?.score || 0,
        newLives: gameState?.lives || 0,
        gameEnded: true,
        correctCardId: ''
      };
    }

    const currentRound = matchingMayhemState.currentRound;
    if (!currentRound || !matchingMayhemState.isRoundActive) {
      logger.info(`Card selection ignored for room ${roomId}: no active round`);
      return {
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        correctCardId: currentRound?.correctCardId || ''
      };
    }

    // Clear all round timers since round is ending
    this.clearRoundTimers(roomId);

    const isCorrect = cardId === currentRound.correctCardId;

    if (isCorrect) {
      // CORRECT ANSWER
      // Calculate points based on reaction time
      const timeElapsed = Date.now() - (matchingMayhemState.roundStartTime || Date.now());
      const timeRemaining = Math.max(0, MATCHING_MAYHEM_CONFIG.ROUND_TIME - timeElapsed);
      const timePercentage = timeRemaining / MATCHING_MAYHEM_CONFIG.ROUND_TIME;

      // Calculate score: higher percentage for faster response
      const points = Math.floor(
        MATCHING_MAYHEM_CONFIG.SCORING.MIN_ROUND_SCORE +
        (MATCHING_MAYHEM_CONFIG.SCORING.MAX_ROUND_SCORE - MATCHING_MAYHEM_CONFIG.SCORING.MIN_ROUND_SCORE) * timePercentage
      );

      // Update score
      this.gameService.updateScore(roomId, points, "add");

      logger.info(`Correct card selection in room ${roomId}: card ${cardId}, points ${points}, new score ${gameState.score}`);

      // Setup next round and get the next round data
      const nextRound = this.setupNextRound(roomId);

      return {
        success: true,
        isCorrect: true,
        points,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        correctCardId: currentRound.correctCardId,
        nextRound: nextRound || undefined
      };
    } else {
      // WRONG ANSWER
      // Apply penalty
      const penalty = MATCHING_MAYHEM_CONFIG.SCORING.WRONG_ANSWER_PENALTY;
      this.gameService.updateScore(roomId, penalty, "subtract");

      // Deduct life
      const livesResult = this.gameService.deductLife(roomId);

      // Check if game should end
      if (livesResult.gameEnded) {
        this.endGame(roomId, 'no_lives');
        return {
          success: true,
          isCorrect: false,
          points: penalty,
          newScore: gameState.score,
          newLives: livesResult.newLives,
          gameEnded: livesResult.gameEnded,
          correctCardId: currentRound.correctCardId
        };
      } else {
        // If game hasn't ended, set up next round after a wrong answer
        const nextRound = this.setupNextRound(roomId);

        logger.error(`selected ${cardId}, correct: ${currentRound.correctCardId}`);

        logger.info(`Wrong card selection in room ${roomId}: card ${cardId}, penalty ${penalty}, new score ${gameState.score}, lives ${livesResult.newLives}, game ended: ${livesResult.gameEnded}`);

        return {
          success: true,
          isCorrect: false,
          points: penalty,
          newScore: gameState.score,
          newLives: livesResult.newLives,
          gameEnded: livesResult.gameEnded,
          correctCardId: currentRound.correctCardId,
          nextRound: nextRound || undefined
        };
      }
    }
  }

  /**
   * Get current round data (server-side with answer info)
   */
  getCurrentRound(roomId: string): ServerRoundData | null {
    const matchingMayhemState = this.gameStates.get(roomId);
    return matchingMayhemState?.currentRound || null;
  }

  /**
   * Setup socket event handlers for Matching Mayhem
   */
  public setupSocketHandlers(_io: Server, socket: Socket): void {
    // Generic game init event
    socket.on('init', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameInit(socket, data);
      }
    });

    // Generic game start event
    socket.on('start', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameStart(socket, data);
      }
    });

    // Generic game end event
    socket.on('end', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameEnd(socket, data);
      }
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      if (data.gameId === GAME_TYPES.MATCHING_MAYHEM) {
        this.handleGameAction(socket, data);
      }
    });
  }

  /**
   * Handle game initialization
   */
  private handleGameInit(socket: Socket, data: GameStartData): void {
    const { roomId, gameId, submitScoreId } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game initialization'
      });
      return;
    }

    // Store submitScoreId in game session for later use
    if (submitScoreId) {
      console.log(`Matching Mayhem game session ${roomId} - submitScoreId: ${submitScoreId}`);
      // TODO: Store submitScoreId in game session data for score submission
    }

    try {
      // Initialize the game (create hashmap, prepare state, but don't start timers)
      const result = this.initializeGame(roomId, socket);

      if (result.success && result.gameState) {
        // Get first round data
        const firstRound = this.getCurrentRound(roomId);

        if (firstRound) {
          const gameInitData = {
            gameState: {
              score: result.gameState.score,
              lives: result.gameState.lives,
              isActive: false, // Not active yet, just initialized
              startTime: null
            },
            firstRound: this.toClientRoundData(firstRound),
            message: 'Game initialized!'
          };

          socket.emit('initialized', gameInitData);
          logger.info(`${gameId} game initialized in room ${roomId}`);
        } else {
          socket.emit('error', {
            message: 'Failed to generate first round'
          });
        }
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to initialize game'
        });
      }
    } catch (error) {
      logger.error(`Error initializing ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game start
   */
  private handleGameStart(socket: Socket, data: GameStartData): void {
    const { roomId, gameId, submitScoreId } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game start'
      });
      return;
    }

    // Store submitScoreId in game session for later use
    if (submitScoreId) {
      console.log(`Matching Mayhem game session ${roomId} - submitScoreId: ${submitScoreId}`);
      // TODO: Store submitScoreId in game session data for score submission
    }

    try {
      // Try to start the previously initialized game, fall back to legacy behavior
      let result = this.startGame(roomId, socket);

      // If game wasn't initialized, fall back to legacy initialize-and-start
      if (!result.success && result.message?.includes('not initialized')) {
        console.log(`Game not initialized in room ${roomId}, falling back to legacy initialize-and-start`);
        result = this.initializeAndStartGame(roomId, socket);
      }

      if (result.success && result.gameState) {
        // Get first round data
        const firstRound = this.getCurrentRound(roomId);

        if (firstRound) {
          const gameStartedData: MatchingMayhemGameStartedData = {
            gameState: {
              score: result.gameState.score,
              lives: result.gameState.lives,
              isActive: result.gameState.status === 'active',
              startTime: result.gameState.startTime
            },
            firstRound: this.toClientRoundData(firstRound),
            message: 'Game started!'
          };

          socket.emit('started', gameStartedData);
          logger.info(`${gameId} game started in room ${roomId}`);
        } else {
          socket.emit('error', {
            message: 'Failed to generate first round'
          });
        }
      } else {
        socket.emit('error', {
          message: result.message || 'Failed to start game'
        });
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game end
   */
  private handleGameEnd(socket: Socket, data: GameEndData): void {
    const { roomId, gameId, reason } = data;

    if (!roomId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for game end'
      });
      return;
    }

    try {
      this.endGame(roomId, reason || 'manual');
      // Note: endGame() method already emitted the 'ended' event

      logger.info(`${gameId} game ended in room ${roomId}, reason: ${reason || 'manual'}`);
    } catch (error) {
      logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle game action
   */
  private handleGameAction(socket: Socket, data: GameActionData): void {
    const { roomId, gameId, action } = data;

    if (!roomId || !gameId || !action) {
      socket.emit('error', {
        message: 'Missing required data for game action'
      });
      return;
    }

    try {
      // Handle specific action types for Matching Mayhem
      switch (action.type) {
        case 'card_select':
          this.handleCardSelectAction(socket, data as CardSelectActionData);
          break;
        default:
          socket.emit('error', {
            message: `Unknown action type: ${action.type}`
          });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle card select action
   */
  private handleCardSelectAction(socket: Socket, data: CardSelectActionData): void {
    const { roomId, gameId, action } = data;
    const { cardId, reactionTime, clickTime } = action.data;

    if (!roomId || !cardId || !gameId) {
      socket.emit('error', {
        message: 'Missing required data for card select action'
      });
      return;
    }

    try {
      // Validate the click timing
      const currentTime = Date.now();
      const timeDiff = Math.abs(currentTime - (clickTime || currentTime));
      if (timeDiff > 5000) { // 5 seconds tolerance
        socket.emit('error', {
          message: 'Click time too far from current time'
        });
        return;
      }

      // Process the card selection
      const result = this.handleCardSelection(roomId, cardId, reactionTime || 0);

      if (result.success) {
        // Get next round if available (convert to client-safe data)
        const serverNextRound = result.gameEnded ? undefined : result.nextRound;
        const nextRound = serverNextRound ? this.toClientRoundData(serverNextRound) : undefined;

        const actionResultData: MatchingMayhemActionResultData = {
          actionType: 'card_select',
          data: {
            cardId,
            isCorrect: result.isCorrect,
            points: result.points,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            nextRound,
            correctCardId: result.correctCardId
          }
        };

        // Send action result to client
        socket.emit('action_result', actionResultData);

        // Note: If game ended, the endGame() method already emitted the 'ended' event

        logger.info(`Card select action processed in room ${roomId}: card ${cardId}, correct: ${result.isCorrect}, score: ${result.newScore}`);
      } else {
        socket.emit('error', {
          message: 'Failed to process card selection'
        });
      }
    } catch (error) {
      logger.error(`Error processing card select action in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }
}
