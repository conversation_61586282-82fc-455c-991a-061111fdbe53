# Game Initialization Split

This document explains the new split game initialization and start functionality.

## Overview

Previously, the `startGame()` method performed both game initialization and game starting in a single operation. This has been split into two separate methods:

1. **`initGame()`** - Initializes game logic, creates hashmaps, prepares state
2. **`startGame()`** - Starts timers and enables game event acceptance

## Client-Side Changes

### Socket Client (`client/src/lib/socket/client.ts`)

#### New Methods

```typescript
// Initialize game (prepare state, create hashmaps, etc.)
initGame(): void

// Start game (start timers, enable events)
startGame(): void
```

#### New Socket Events

- **Outgoing**: `'init'` - Sent when initializing game
- **Incoming**: `'initialized'` - Received when game is initialized

### Game State (`client/src/lib/stores/gameState.ts`)

#### New State Field

```typescript
interface GameState {
  // ... existing fields
  isInitialized: boolean; // Tracks if game has been initialized
}
```

#### Updated Actions

- `initGame()` - Now sets `isInitialized: true`

## Server-Side Changes

### Game Controllers

All game controllers now have split methods:

#### New Methods

```typescript
// Initialize game (create state, hashmaps, but no timers)
initializeGame(roomId: string, socket: Socket): GameInitResult

// Start previously initialized game (start timers, enable events)
startGame(roomId: string, socket: Socket): GameInitResult

// Legacy method (deprecated)
initializeAndStartGame(roomId: string, socket: Socket): GameInitResult
```

#### New Socket Event Handlers

- **`'init'`** - Handles game initialization requests
- **`'start'`** - Now handles starting of previously initialized games

#### New Socket Events Emitted

- **`'initialized'`** - Sent when game is successfully initialized

## Usage Examples

### New Split Flow

```typescript
// Step 1: Initialize the game
socketClient.initGame();
// Server creates hashmaps, prepares state, but no timers start

// Step 2: Start the game (when ready)
socketClient.startGame();
// Server starts timers and enables event acceptance
```

### Legacy Flow (Still Supported)

```typescript
// Single operation (uses deprecated method internally)
socketClient.startGame();
// Server does both initialization and starting
```

## Benefits

1. **Better Control**: Separate initialization from starting allows for preparation time
2. **Cleaner State Management**: Clear distinction between "initialized" and "active" states
3. **Flexibility**: Can initialize game early, start when ready
4. **Debugging**: Easier to debug initialization vs. runtime issues

## Game Flow States

1. **Not Initialized**: `isInitialized: false, isPlaying: false`
2. **Initialized**: `isInitialized: true, isPlaying: false`
3. **Playing**: `isInitialized: true, isPlaying: true`
4. **Ended**: `isInitialized: true, isPlaying: false, gameOver: true`

## Implementation Status

### Completed
- ✅ Client-side socket client split
- ✅ Client-side game state updates
- ✅ MatchingMayhemController split
- ✅ FingerFrenzyController split
- ✅ Socket event handlers

### Remaining
- ⏳ BingoController split
- ⏳ NumberSequenceController split
- ⏳ Integration testing
- ⏳ Update game scenes to use new flow

## Migration Guide

### For Existing Code

Existing code using `socketClient.startGame()` will continue to work as it internally calls the legacy `initializeAndStartGame()` method.

### For New Code

Use the new split approach:

```typescript
// Initialize first
await socketClient.initGame();

// Wait for any preparation (optional)
await somePreparationLogic();

// Start when ready
await socketClient.startGame();
```

## Testing

See `client/src/lib/socket/game-flow-example.ts` for a complete example demonstrating both the new split flow and legacy flow.
