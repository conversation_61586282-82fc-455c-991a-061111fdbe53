/**
 * Example demonstrating the split game initialization and start flow
 */

import { socketClient } from './client';
import { gameActions } from '../stores';

export class GameFlowExample {
  private roomId: string;
  private gameId: string;

  constructor(roomId: string, gameId: string) {
    this.roomId = roomId;
    this.gameId = gameId;
  }

  /**
   * Example of the new split flow:
   * 1. Initialize game (create hashmap, prepare state)
   * 2. Start game (start timers, enable events)
   */
  async demonstrateSplitFlow() {
    console.log('=== Demonstrating Split Game Flow ===');

    // Step 1: Initialize the game
    console.log('Step 1: Initializing game...');
    await this.initializeGame();

    // Wait a bit to simulate preparation time
    await this.delay(2000);

    // Step 2: Start the game
    console.log('Step 2: Starting game...');
    await this.startGame();

    console.log('Game flow complete!');
  }

  /**
   * Initialize the game (creates hashmap, prepares state, but no timers)
   */
  private async initializeGame(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Set up one-time listener for initialization response
      const handleInitialized = (data: any) => {
        console.log('Game initialized successfully:', data);
        socketClient.removeCustomEventListener('initialized', handleInitialized);
        resolve();
      };

      const handleError = (error: any) => {
        console.error('Game initialization failed:', error);
        socketClient.removeCustomEventListener('error', handleError);
        reject(error);
      };

      // Add listeners
      socketClient.addCustomEventListener('initialized', handleInitialized);
      socketClient.addCustomEventListener('error', handleError);

      // Initialize the game
      socketClient.initGame();

      // Timeout after 10 seconds
      setTimeout(() => {
        socketClient.removeCustomEventListener('initialized', handleInitialized);
        socketClient.removeCustomEventListener('error', handleError);
        reject(new Error('Game initialization timeout'));
      }, 10000);
    });
  }

  /**
   * Start the game (starts timers, enables event acceptance)
   */
  private async startGame(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Set up one-time listener for start response
      const handleStarted = (data: any) => {
        console.log('Game started successfully:', data);
        socketClient.removeCustomEventListener('started', handleStarted);
        resolve();
      };

      const handleError = (error: any) => {
        console.error('Game start failed:', error);
        socketClient.removeCustomEventListener('error', handleError);
        reject(error);
      };

      // Add listeners
      socketClient.addCustomEventListener('started', handleStarted);
      socketClient.addCustomEventListener('error', handleError);

      // Start the game
      socketClient.startGame();

      // Timeout after 10 seconds
      setTimeout(() => {
        socketClient.removeCustomEventListener('started', handleStarted);
        socketClient.removeCustomEventListener('error', handleError);
        reject(new Error('Game start timeout'));
      }, 10000);
    });
  }

  /**
   * Example of the legacy flow (for comparison)
   */
  async demonstrateLegacyFlow() {
    console.log('=== Demonstrating Legacy Game Flow ===');

    return new Promise((resolve, reject) => {
      // Set up one-time listener for start response
      const handleStarted = (data: any) => {
        console.log('Game started successfully (legacy):', data);
        socketClient.removeCustomEventListener('started', handleStarted);
        resolve(data);
      };

      const handleError = (error: any) => {
        console.error('Game start failed (legacy):', error);
        socketClient.removeCustomEventListener('error', handleError);
        reject(error);
      };

      // Add listeners
      socketClient.addCustomEventListener('started', handleStarted);
      socketClient.addCustomEventListener('error', handleError);

      // Start the game (legacy - does both init and start)
      socketClient.startGame();

      // Timeout after 10 seconds
      setTimeout(() => {
        socketClient.removeCustomEventListener('started', handleStarted);
        socketClient.removeCustomEventListener('error', handleError);
        reject(new Error('Game start timeout'));
      }, 10000);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Usage example:
// const gameFlow = new GameFlowExample('room-123', 'finger-frenzy');
// gameFlow.demonstrateSplitFlow();
