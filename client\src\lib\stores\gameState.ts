import { get, writable } from 'svelte/store';

export interface GameState {
	score: number;
	time: number;
	totalTime: number;
	maxLives: number;
	lives: number;
	isLoading: boolean;
	loadingProgress: number;
	isInitialized: boolean;
	isCountdown: boolean;
	isPlaying: boolean;
	isPaused: boolean;
	gameOver: boolean;
	opponentScore: number | null;
	roomId: string | null;
	authToken: string | null;
	submitScoreId: string | null;
	gameId: string | null;
}

const initialState: GameState = {
	score: 0,
	time: 30,
	totalTime: 30,
	maxLives: 3,
	lives: 3,
	isLoading: true,
	loadingProgress: 0,
	isInitialized: false,
	isCountdown: false,
	isPlaying: false,
	isPaused: false,
	gameOver: false,
	opponentScore: null,
	roomId: null,
	authToken: null,
	submitScoreId: null,
	gameId: null
};

export const gameState = writable<GameState>(initialState);

// Helper functions for updating game state
export const gameActions = {
	updateLoadingProgress: (progress: number) => {
		console.log('[GameState] Updating loading progress:', progress);
		gameState.update(state => ({ ...state, loadingProgress: progress }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	updateScore: (score: number) => {
		console.log('[GameState] Updating score:', score);
		gameState.update(state => ({ ...state, score }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	updateTime: (time: number) => {
		console.log('[GameState] Updating time:', time);
		gameState.update(state => ({ ...state, time }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	updateLives: (lives: number) => {
		console.log('[GameState] Updating lives:', lives);
		gameState.update(state => ({ ...state, lives }));
		console.log('[GameState] New state:', get(gameState));
	},

	preloadComplete: () => {
		console.log('[GameState] Preload complete');
		gameState.update(state => ({ ...state, isLoading: false }));
		console.log('[GameState] New state:', get(gameState));
	},

	initGame: () => {
		console.log('[GameState] Initializing game');
		gameState.update(state => ({
			...state,
			isInitialized: true,
			isCountdown: true,
			isPlaying: false,
			isPaused: false,
			gameOver: false  }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	startGame: () => {
		console.log('[GameState] Starting game');
		gameState.update(state => ({ 
			...state, 
			isPlaying: true, 
		}));
		console.log('[GameState] New state:', get(gameState));
	},
	
	pauseGame: () => {
		console.log('[GameState] Pausing game');
		gameState.update(state => ({ ...state, isPaused: true }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	resumeGame: () => {
		console.log('[GameState] Resuming game');
		gameState.update(state => ({ ...state, isPaused: false }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	endGame: () => {
		console.log('[GameState] Ending game');
		gameState.update(state => ({ 
			...state, 
			isPlaying: false, 
			gameOver: true 
		}));
		console.log('[GameState] New state:', get(gameState));
	},
	
	resetGame: () => {
		console.log('[GameState] Resetting game');
		gameState.set(initialState);
		console.log('[GameState] New state:', get(gameState));
	},
	
	setOpponentScore: (opponentScore: number) => {
		console.log('[GameState] Setting opponent score:', opponentScore);
		gameState.update(state => ({ ...state, opponentScore }));
		console.log('[GameState] New state:', get(gameState));
	},
	
	setRoomData: (roomId: string, authToken: string, submitScoreId: string) => {
		console.log('[GameState] Setting room data:', { roomId, authToken, submitScoreId });
		gameState.update(state => ({ 
			...state, 
			roomId, 
			authToken, 
			submitScoreId 
		}));
		console.log('[GameState] New state:', get(gameState));
	},
	
	setGameId: (gameId: string) => {
		console.log('[GameState] Setting game ID:', gameId);
		gameState.update(state => ({ ...state, gameId }));
		console.log('[GameState] New state:', get(gameState));
	}
};
